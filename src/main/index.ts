import { app, Menu } from "electron"
import { electronApp, optimizer } from "@electron-toolkit/utils"
import {
  createMainWindow,
  createPanelWindow,
  createSetupWindow,
  makePanelWindowClosable,
  WINDOWS,
} from "./window"
import { listenTo<PERSON>eyboardEvents } from "./keyboard"
import { registerIpcMain } from "@egoist/tipc/main"
import { router } from "./tipc"
import { registerServeProtocol, registerServeSchema } from "./serve"
import { createAppMenu } from "./menu"
import { initTray } from "./tray"
import { isAccessibilityGranted } from "./utils"

// Disable Mach port rendezvous for Mac App Store compatibility
// This prevents the SIGTRAP crash in MAS builds
if (process.mas) {
  process.env.ELECTRON_DISABLE_MACH_PORT_RENDEZVOUS = '1'
}

registerServeSchema()

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(async () => {
  try {
    // Set app user model id for windows
    electronApp.setAppUserModelId(process.env.APP_ID)

    const accessibilityGranted = isAccessibilityGranted()

    // Register IPC and protocols first
    registerIpcMain(router)
    registerServeProtocol()

    // Create windows before setting menu to ensure proper initialization order
    if (accessibilityGranted) {
      createMainWindow()
    } else {
      createSetupWindow()
    }

    createPanelWindow()

    // Set application menu after windows are created with a small delay
    // This helps prevent menu-related crashes on macOS during launch
    setTimeout(() => {
      try {
        Menu.setApplicationMenu(createAppMenu())
      } catch (error) {
        console.error('Failed to set application menu:', error)
        // Fallback to null menu if creation fails
        Menu.setApplicationMenu(null)
      }
    }, 100)

    listenToKeyboardEvents()

    initTray()

    import("./updater").then((res) => res.init()).catch(console.error)
  } catch (error) {
    console.error('Error during app initialization:', error)
    // Ensure the app doesn't crash silently
    app.quit()
  }

  // Default open or close DevTools by F12 in development
  // and ignore CommandOrControl + R in production.
  // see https://github.com/alex8088/electron-toolkit/tree/master/packages/utils
  app.on("browser-window-created", (_, window) => {
    optimizer.watchWindowShortcuts(window)
  })

  app.on("activate", function () {
    const accessibilityGranted = isAccessibilityGranted()
    if (accessibilityGranted) {
      if (!WINDOWS.get("main")) {
        createMainWindow()
      }
    } else {
      if (!WINDOWS.get("setup")) {
        createSetupWindow()
      }
    }
  })

  app.on("before-quit", () => {
    makePanelWindowClosable()
  })
})

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on("window-all-closed", () => {
  if (process.platform !== "darwin") {
    app.quit()
  }
})

// In this file you can include the rest of your app"s specific main process
// code. You can also put them in separate files and require them here.
