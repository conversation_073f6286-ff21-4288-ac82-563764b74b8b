#!/usr/bin/env node

/**
 * Simple test script to verify the app can launch without crashing
 * This can be used to test the fixes before submitting to Apple
 */

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('Testing SpeakMCP launch...');

// Build the app first
console.log('Building app...');
const buildProcess = spawn('npm', ['run', 'build'], {
  stdio: 'inherit',
  cwd: __dirname
});

buildProcess.on('close', (code) => {
  if (code !== 0) {
    console.error('Build failed with code:', code);
    process.exit(1);
  }

  console.log('Build successful, testing launch...');

  // Test launch
  const testProcess = spawn('npm', ['run', 'start'], {
    stdio: 'inherit',
    cwd: __dirname
  });

  // Kill the test after 10 seconds if it's still running (success)
  const timeout = setTimeout(() => {
    console.log('App launched successfully for 10 seconds, killing...');
    testProcess.kill('SIGTERM');
    console.log('✅ Launch test passed!');
    process.exit(0);
  }, 10000);

  testProcess.on('close', (code) => {
    clearTimeout(timeout);
    if (code === 0) {
      console.log('✅ App exited cleanly');
    } else {
      console.error('❌ App crashed with code:', code);
      process.exit(1);
    }
  });

  testProcess.on('error', (error) => {
    clearTimeout(timeout);
    console.error('❌ Launch test failed:', error);
    process.exit(1);
  });
});

buildProcess.on('error', (error) => {
  console.error('Build process failed:', error);
  process.exit(1);
});
