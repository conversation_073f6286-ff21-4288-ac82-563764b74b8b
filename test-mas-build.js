#!/usr/bin/env node

/**
 * Comprehensive test script to verify both regular and MAS builds work
 */

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🧪 Testing SpeakMCP builds...\n');

// Test 1: Regular build
console.log('1️⃣ Testing regular build...');
const buildProcess = spawn('npm', ['run', 'build:unpack'], {
  stdio: 'inherit',
  cwd: __dirname
});

buildProcess.on('close', (code) => {
  if (code !== 0) {
    console.error('❌ Regular build failed with code:', code);
    process.exit(1);
  }

  console.log('✅ Regular build successful!\n');

  // Test 2: Check if regular build app exists
  const regularAppPath = path.join(__dirname, 'dist/mac-arm64/SpeakMCP.app');
  if (!fs.existsSync(regularAppPath)) {
    console.error('❌ Regular build app not found at:', regularAppPath);
    process.exit(1);
  }
  console.log('✅ Regular build app exists\n');

  // Test 3: MAS dev build
  console.log('2️⃣ Testing MAS dev build...');
  const masDevProcess = spawn('npx', ['electron-builder', '--mac', 'mas-dev', '--config', 'electron-builder.config.cjs'], {
    stdio: 'inherit',
    cwd: __dirname
  });

  masDevProcess.on('close', (masCode) => {
    if (masCode !== 0) {
      console.error('❌ MAS dev build failed with code:', masCode);
      process.exit(1);
    }

    console.log('✅ MAS dev build successful!\n');

    // Test 4: Check if MAS dev build app exists
    const masDevAppPath = path.join(__dirname, 'dist/mas-dev-arm64/SpeakMCP.app');
    if (!fs.existsSync(masDevAppPath)) {
      console.error('❌ MAS dev build app not found at:', masDevAppPath);
      process.exit(1);
    }
    console.log('✅ MAS dev build app exists\n');

    // Test 5: Check code signing
    console.log('3️⃣ Checking code signing...');
    const codesignProcess = spawn('codesign', ['-dv', '--verbose=4', masDevAppPath], {
      stdio: 'pipe',
      cwd: __dirname
    });

    let codesignOutput = '';
    codesignProcess.stderr.on('data', (data) => {
      codesignOutput += data.toString();
    });

    codesignProcess.on('close', (signCode) => {
      if (signCode !== 0) {
        console.error('❌ Code signing verification failed');
        process.exit(1);
      }

      console.log('✅ Code signing verification passed');
      console.log('📋 Signing details:');
      console.log(codesignOutput);

      // Final summary
      console.log('\n🎉 ALL TESTS PASSED!');
      console.log('\n📊 Summary:');
      console.log('✅ Regular build: WORKING');
      console.log('✅ MAS dev build: WORKING');
      console.log('✅ Code signing: WORKING');
      console.log('\n🚀 Ready for Apple App Store submission!');
      
      process.exit(0);
    });

    codesignProcess.on('error', (error) => {
      console.error('❌ Code signing check failed:', error);
      process.exit(1);
    });
  });

  masDevProcess.on('error', (error) => {
    console.error('❌ MAS dev build process failed:', error);
    process.exit(1);
  });
});

buildProcess.on('error', (error) => {
  console.error('❌ Regular build process failed:', error);
  process.exit(1);
});
