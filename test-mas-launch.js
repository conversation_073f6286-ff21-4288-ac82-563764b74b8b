#!/usr/bin/env node

/**
 * Test script specifically for Mac App Store build
 * This tests the actual MAS build that gets submitted to Apple
 */

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('Testing SpeakMCP Mac App Store build...');

// First, ensure we have the .env file loaded
console.log('Checking environment setup...');
const envPath = path.join(__dirname, '.env');
if (!fs.existsSync(envPath)) {
  console.error('❌ .env file not found. Please ensure signing certificates are configured.');
  process.exit(1);
}

// Build the MAS version
console.log('Building Mac App Store version...');
const buildProcess = spawn('npm', ['run', 'build:mas'], {
  stdio: 'inherit',
  cwd: __dirname,
  env: { ...process.env, NODE_ENV: 'production' }
});

buildProcess.on('close', (code) => {
  if (code !== 0) {
    console.error('❌ MAS build failed with code:', code);
    process.exit(1);
  }

  console.log('✅ MAS build successful!');

  // Check if the MAS app was created
  const masAppPath = path.join(__dirname, 'dist', 'mas-arm64', 'SpeakMCP.app');
  const masAppPathX64 = path.join(__dirname, 'dist', 'mas-x64', 'SpeakMCP.app');

  let appPath = null;
  if (fs.existsSync(masAppPath)) {
    appPath = masAppPath;
    console.log('Found ARM64 MAS app at:', appPath);
  } else if (fs.existsSync(masAppPathX64)) {
    appPath = masAppPathX64;
    console.log('Found x64 MAS app at:', appPath);
  } else {
    console.error('❌ MAS app not found in expected locations');
    console.log('Expected locations:');
    console.log('  -', masAppPath);
    console.log('  -', masAppPathX64);
    process.exit(1);
  }

  console.log('Testing MAS app launch...');

  // Test launch the MAS app directly with more verbose output
  const testProcess = spawn('open', ['-a', appPath, '--wait-apps'], {
    stdio: 'pipe',
    cwd: __dirname
  });

  // Kill the test after 15 seconds if it's still running (success)
  const timeout = setTimeout(() => {
    console.log('✅ MAS app launched successfully for 15 seconds!');

    // Try to quit the app gracefully
    spawn('osascript', ['-e', 'tell application "SpeakMCP" to quit'], {
      stdio: 'ignore'
    });

    setTimeout(() => {
      console.log('✅ MAS launch test passed!');
      process.exit(0);
    }, 2000);
  }, 15000);

  testProcess.on('close', (code) => {
    clearTimeout(timeout);
    if (code === 0) {
      console.log('✅ MAS app launched successfully');
    } else {
      console.error('❌ MAS app failed to launch with code:', code);
      process.exit(1);
    }
  });

  testProcess.on('error', (error) => {
    clearTimeout(timeout);
    console.error('❌ MAS launch test failed:', error);
    process.exit(1);
  });

  // Capture any output
  testProcess.stdout.on('data', (data) => {
    console.log('App output:', data.toString());
  });

  testProcess.stderr.on('data', (data) => {
    console.error('App error:', data.toString());
  });
});

buildProcess.on('error', (error) => {
  console.error('❌ Build process failed:', error);
  process.exit(1);
});
