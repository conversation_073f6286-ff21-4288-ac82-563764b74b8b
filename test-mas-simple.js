#!/usr/bin/env node

/**
 * Simple test to check if the MAS app can launch without immediate crash
 */

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🧪 Testing MAS app launch stability...');

// Check if MAS app exists
const masAppPath = path.join(__dirname, 'dist', 'mas-arm64', 'SpeakMCP.app');
if (!fs.existsSync(masAppPath)) {
  console.error('❌ MAS app not found at:', masAppPath);
  console.log('Please run the MAS build first');
  process.exit(1);
}

console.log('✅ Found MAS app at:', masAppPath);

// Test 1: Check if the app binary exists and is executable
const binaryPath = path.join(masAppPath, 'Contents', 'MacOS', 'SpeakMCP');
if (!fs.existsSync(binaryPath)) {
  console.error('❌ App binary not found at:', binaryPath);
  process.exit(1);
}

console.log('✅ App binary found');

// Test 2: Try to launch the app and see if it crashes immediately
console.log('🚀 Attempting to launch MAS app...');

const testProcess = spawn(binaryPath, [], {
  stdio: 'pipe',
  detached: false
});

let crashed = false;
let output = '';
let errorOutput = '';

testProcess.stdout.on('data', (data) => {
  output += data.toString();
});

testProcess.stderr.on('data', (data) => {
  errorOutput += data.toString();
});

testProcess.on('close', (code, signal) => {
  crashed = true;
  console.log(`\n📊 Test Results:`);
  console.log(`Exit code: ${code}`);
  console.log(`Signal: ${signal}`);
  
  if (code === 0) {
    console.log('✅ App exited cleanly');
  } else {
    console.log('❌ App crashed or exited with error');
  }
  
  if (output) {
    console.log('\n📝 Standard Output:');
    console.log(output);
  }
  
  if (errorOutput) {
    console.log('\n🚨 Error Output:');
    console.log(errorOutput);
  }
  
  // Analyze the error
  if (errorOutput.includes('mach_port_rendezvous')) {
    console.log('\n🔍 Analysis: Mach port error detected');
    console.log('This is likely related to Mac App Store sandboxing restrictions.');
    console.log('The app may need additional entitlements or code modifications for MAS compatibility.');
  }
  
  process.exit(code === 0 ? 0 : 1);
});

testProcess.on('error', (error) => {
  console.error('❌ Failed to launch app:', error);
  process.exit(1);
});

// Kill the process after 10 seconds if it's still running (success case)
setTimeout(() => {
  if (!crashed) {
    console.log('\n✅ SUCCESS: App has been running for 10 seconds without crashing!');
    console.log('This indicates the MAS build is stable and ready for submission.');
    testProcess.kill('SIGTERM');
    
    // Give it a moment to clean up
    setTimeout(() => {
      if (!crashed) {
        testProcess.kill('SIGKILL');
      }
      process.exit(0);
    }, 2000);
  }
}, 10000);
