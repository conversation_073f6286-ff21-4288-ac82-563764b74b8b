{
  "extends": "@electron-toolkit/tsconfig/tsconfig.web.json",
  "include": [
    "src/shared/**/*",
    "src/main/*.ts",
    "src/renderer/src/env.d.ts",
    "src/renderer/src/**/*",
    "src/renderer/src/**/*.tsx",
    "src/preload/*.d.ts"
  ],
  "exclude": ["src/renderer/src/**/__tests__/**/*"],
  "compilerOptions": {
    "composite": true,
    "jsx": "react-jsx",
    "moduleResolution": "Bundler",
    "noUnusedLocals": false,
    "baseUrl": ".",
    "paths": {
      "@renderer/*": [
        "src/renderer/src/*"
      ],
      "~/*": [
        "src/renderer/src/*"
      ],
      "@shared/*": [
        "src/shared/*"
      ],
    }
  }
}
